import { Schema } from "effect";
import { Client } from "~/client/service/model/client";
import { Worker } from "~/worker/service/model/worker";

export const Session = Schema.Struct({
	id: Schema.String,
	client: Client,
	worker: Worker,
	turnId: Schema.String,
	day: Schema.Number,
	time: Schema.Number,
	note: Schema.NullOr(Schema.String),
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Session = typeof Session.Type;

export const CreateSession = Schema.Struct({
	clientId: Schema.String,
	workerId: Schema.String,
	turnId: Schema.String,
	day: Schema.Number,
	time: Schema.Number,
	note: Schema.optional(Schema.NullOr(Schema.String)),
});
export type CreateSession = typeof CreateSession.Type;

export const UpdateSession = Schema.Struct({
	id: Schema.String,
	clientId: Schema.String,
	workerId: Schema.String,
	turnId: Schema.String,
	day: Schema.Number,
	time: Schema.Number,
	note: Schema.optional(Schema.NullOr(Schema.String)),
});
export type UpdateSession = typeof UpdateSession.Type;

export const CreateManySession = Schema.Struct({
	sessions: Schema.Array(CreateSession),
});
export type CreateManySession = typeof CreateManySession.Type;

export const DeleteManySession = Schema.Struct({
	ids: Schema.Array(Schema.String),
});
export type DeleteManySession = typeof DeleteManySession.Type;
