import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { clientOptions } from "~/modules/client/hooks/client-options";
import type { Client } from "~/modules/client/service/model/client";
import { sessionOptions } from "~/modules/session/hooks/session-options";
import useCreateSession from "~/modules/session/hooks/use-create-session";
import useDeleteSession from "~/modules/session/hooks/use-delete-session";
import useUpdateSession from "~/modules/session/hooks/use-update-session";
import { workerOptions } from "~/modules/worker/hooks/worker-options";
import type { Worker } from "~/modules/worker/service/model/worker";
import type { Session } from "../../service/model/session";
import { sessionStore } from "../../store/session";

export interface SessionModalProps {
	isOpen: boolean;
	onClose: () => void;
	dayIndex: number;
	timeIndex: number;
	existingSession: Session | undefined;
}

export default function useSessionModal({
	isOpen,
	onClose,
	dayIndex,
	timeIndex,
	existingSession,
}: SessionModalProps) {
	const svc = useService();
	const { mutate: createSession } = useCreateSession();
	const { mutate: deleteSession } = useDeleteSession();
	const { mutate: updateSession } = useUpdateSession();
	const {
		selectedClient: preSelectedClient,
		selectedWorker: preSelectedWorker,
		selectedTurn,
	} = useStore(sessionStore);

	const turnId = selectedTurn?.id || "";

	const [selectedClient, setSelectedClient] = useState<Client | null>(null);
	const [selectedWorker, setSelectedWorker] = useState<Worker | null>(null);
	const [note, setNote] = useState<string>("");

	const {
		data: clients,
		error: clientError,
		isPending: clientsPending,
	} = useQuery({
		...clientOptions(svc),
		enabled: isOpen,
	});

	const {
		data: workers,
		error: workerError,
		isPending: workersPending,
	} = useQuery({
		...workerOptions(svc),
		enabled: isOpen,
	});

	// Get ALL sessions to filter availability properly
	const { data: allSessionsData } = useQuery({
		...sessionOptions(svc),
		enabled: isOpen,
	});

	// Filter sessions for the current turn only
	const allSessions = (allSessionsData || []).filter(
		(session) => session.turnId === turnId,
	);

	// Set initial values when modal opens
	useEffect(() => {
		if (isOpen) {
			if (existingSession) {
				setSelectedClient(existingSession.client);
				setSelectedWorker(existingSession.worker);
			} else {
				setSelectedClient(preSelectedClient);
				setSelectedWorker(preSelectedWorker);
			}
		}
	}, [isOpen, existingSession, preSelectedClient, preSelectedWorker]);

	// Initialize note from existing session
	useEffect(() => {
		if (isOpen && existingSession) {
			setNote(existingSession.note || "");
		} else if (isOpen) {
			setNote("");
		}
	}, [isOpen, existingSession]);

	// Reset when modal closes
	useEffect(() => {
		if (!isOpen) {
			setSelectedClient(null);
			setSelectedWorker(null);
			setNote("");
		}
	}, [isOpen]);

	useEffect(() => {
		if (clientError) {
			console.log(getErrorResult(clientError).error);
		}
		if (workerError) {
			console.log(getErrorResult(workerError).error);
		}
	}, [clientError, workerError]);

	// Helper function to check if a client/worker is busy at this time slot
	const isClientBusyAtTimeSlot = (clientId: string) => {
		return allSessions.some(
			(session) =>
				session.client.id === clientId &&
				session.day === dayIndex &&
				session.time === timeIndex &&
				session.id !== existingSession?.id, // Don't exclude current session
		);
	};

	const isWorkerBusyAtTimeSlot = (workerId: string) => {
		return allSessions.some(
			(session) =>
				session.worker.id === workerId &&
				session.day === dayIndex &&
				session.time === timeIndex &&
				session.id !== existingSession?.id, // Don't exclude current session
		);
	};

	// Filter out busy clients and workers
	const availableClients =
		clients?.filter((client) => {
			// Don't filter if this is the current session's client
			if (existingSession && client.id === existingSession.client.id) {
				return true;
			}
			// Filter out busy clients and clients with ID "0" (busy placeholder)
			return client.id !== "0" && !isClientBusyAtTimeSlot(client.id);
		}) || [];

	const availableWorkers =
		workers?.filter((worker) => {
			// Don't filter if this is the current session's worker
			if (existingSession && worker.id === existingSession.worker.id) {
				return true;
			}
			// Filter out busy workers
			return !isWorkerBusyAtTimeSlot(worker.id);
		}) || [];

	const clientOptions_data = availableClients.map((client) => ({
		value: client.id,
		label: `${client.person.name} ${client.person.fatherLastName} ${client.person.motherLastName}`,
	}));

	const workerOptions_data = availableWorkers.map((worker) => ({
		value: worker.id,
		label: `${worker.person.name} ${worker.person.fatherLastName} ${worker.person.motherLastName}`,
	}));

	const handleClientChange = (
		option: { value: string | number; label: string } | null,
	) => {
		if (option) {
			const client = availableClients.find((c) => c.id === option.value);
			setSelectedClient(client || null);
		} else {
			setSelectedClient(null);
		}
	};

	const handleWorkerChange = (
		option: { value: string | number; label: string } | null,
	) => {
		if (option) {
			const worker = availableWorkers.find((w) => w.id === option.value);
			setSelectedWorker(worker || null);
		} else {
			setSelectedWorker(null);
		}
	};

	// Helper function to auto-save session
	const saveSession = () => {
		if (!(selectedClient && selectedWorker)) return;
		if (!(selectedWorker && selectedClient)) return;

		let finalClientId: string;
		let finalWorkerId: string;

		if (existingSession) {
			// For existing sessions, use the selected values from the modal
			finalClientId = selectedClient.id;
			finalWorkerId = selectedWorker.id;
		} else if (preSelectedClient) {
			// Client was pre-selected from store, use worker from modal
			finalClientId = preSelectedClient.id;
			finalWorkerId = selectedWorker.id;
		} else if (preSelectedWorker) {
			// Worker was pre-selected from store, use client from modal
			finalWorkerId = preSelectedWorker.id;
			finalClientId = selectedClient.id;
		} else {
			// This shouldn't happen, but handle it just in case
			finalClientId = selectedClient.id;
			finalWorkerId = selectedWorker.id;
		}

		// Check if we're replacing an existing session
		const isReplacing =
			existingSession &&
			(existingSession.client.id !== finalClientId ||
				existingSession.worker.id !== finalWorkerId);

		// Check if we're just updating the note of an existing session
		const isUpdatingNote =
			existingSession &&
			existingSession.client.id === finalClientId &&
			existingSession.worker.id === finalWorkerId &&
			existingSession.note !== note;

		if (isUpdatingNote) {
			// Update existing session with new note
			updateSession(
				{
					id: existingSession.id,
					clientId: finalClientId,
					workerId: finalWorkerId,
					turnId,
					day: dayIndex,
					time: timeIndex,
					note: note || null,
				},
				{
					onSuccess: () => {
						toast.success("Nota actualizada exitosamente");
						onClose();
					},
					onError: (error) => {
						console.log(error);
						const { error: errorResult } = getErrorResult(error);
						toast.error(errorResult.message);
					},
				},
			);
		} else if (isReplacing) {
			// Delete existing session first, then create new one
			deleteSession(existingSession.id, {
				onSuccess: () => {
					// Create new session after successful deletion
					createSession(
						{
							clientId: finalClientId,
							workerId: finalWorkerId,
							turnId,
							day: dayIndex,
							time: timeIndex,
							note: note || null,
						},
						{
							onSuccess: () => {
								toast.success("Sesión reemplazada exitosamente");
								onClose();
							},
							onError: (error) => {
								console.log(error);
								const { error: errorResult } = getErrorResult(error);
								toast.error(errorResult.message);
							},
						},
					);
				},
				onError: (error) => {
					console.log(error);
					const { error: errorResult } = getErrorResult(error);
					toast.error(errorResult.message);
				},
			});
		} else if (!existingSession) {
			// Create new session only if there's no existing session
			createSession(
				{
					clientId: finalClientId,
					workerId: finalWorkerId,
					turnId,
					day: dayIndex,
					time: timeIndex,
					note: note || null,
				},
				{
					onSuccess: () => {
						toast.success("Sesión creada exitosamente");
						onClose();
					},
					onError: (error) => {
						console.log(error);
						const { error: errorResult } = getErrorResult(error);
						toast.error(errorResult.message);
					},
				},
			);
		}
	};

	const handleDelete = () => {
		if (!existingSession) return;

		deleteSession(existingSession.id, {
			onSuccess: () => {
				toast.success("Sesión eliminada exitosamente");
				onClose();
			},
			onError: (error) => {
				console.log(error);
				const { error: errorResult } = getErrorResult(error);
				toast.error(errorResult.message);
			},
		});
	};

	const handleMakeBusy = () => {
		if (!preSelectedWorker) {
			toast.error(
				"Solo se puede marcar como ocupado cuando hay un trabajador seleccionado",
			);
			return;
		}

		// Create a busy session with clientId = "0"
		createSession(
			{
				clientId: "0",
				workerId: preSelectedWorker.id,
				turnId,
				day: dayIndex,
				time: timeIndex,
				note: note || null,
			},
			{
				onSuccess: () => {
					toast.success("Horario marcado como ocupado");
					onClose();
				},
				onError: (error) => {
					console.log(error);
					const { error: errorResult } = getErrorResult(error);
					toast.error(errorResult.message);
				},
			},
		);
	};

	const selectedClientOption = selectedClient
		? {
				value: selectedClient.id,
				label: `${selectedClient.person.name} ${selectedClient.person.fatherLastName} ${selectedClient.person.motherLastName}`,
				data: selectedClient,
			}
		: null;

	const selectedWorkerOption = selectedWorker
		? {
				value: selectedWorker.id,
				label: `${selectedWorker.person.name} ${selectedWorker.person.fatherLastName} ${selectedWorker.person.motherLastName}`,
				data: selectedWorker,
			}
		: null;

	const days = [
		"Domingo",
		"Lunes",
		"Martes",
		"Miércoles",
		"Jueves",
		"Viernes",
		"Sábado",
	];

	return {
		days,
		selectedClientOption,
		selectedWorkerOption,
		preSelectedWorker,
		preSelectedClient,
		clientError,
		workerError,
		clientOptions_data,
		clientsPending,
		workerOptions_data,
		workersPending,
		note,
		selectedClient,
		selectedWorker,
		handleMakeBusy,
		handleWorkerChange,
		handleClientChange,
		setNote,
		handleDelete,
		saveSession,
	};
}
