import CloseModal from "~/core/components/CloseModal";
import ComboBox from "~/core/components/ComboBox";
import { cn } from "~/core/utils/classes";
import { getErrorResult } from "~/core/utils/effectErrors";
import type { SessionModalProps } from "./use-session-modal";
import useSessionModal from "./use-session-modal";

export default function SessionModal({
	isOpen,
	onClose,
	dayIndex,
	timeIndex,
	existingSession,
}: SessionModalProps) {
	const {
		days,
		selectedClientOption,
		selectedWorkerOption,
		preSelectedWorker,
		preSelectedClient,
		clientError,
		workerError,
		clientOptions_data,
		clientsPending,
		workerOptions_data,
		workersPending,
		selectedClient,
		selectedWorker,
		note,
		handleMakeBusy,
		handleClientChange,
		setNote,
		handleWorkerChange,
		handleDelete,
		saveSession,
	} = useSessionModal({
		isOpen,
		onClose,
		dayIndex,
		timeIndex,
		existingSession,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box w-3/12 max-w-5xl">
				<CloseModal onClose={onClose} />
				<h3 className="font-bold text-lg">
					{existingSession ? "Editar Sesión" : "Crear Sesión"}
				</h3>

				<div className="py-4">
					<p className="mb-4 text-base-content/70 text-sm">
						{days[dayIndex]} - Horario {timeIndex + 1}
					</p>

					<div className="space-y-4">
						{preSelectedWorker ? (
							<div>
								{clientError ? (
									<div className="alert alert-error">
										<span>
											Error: {getErrorResult(clientError).error.message}
										</span>
									</div>
								) : (
									<ComboBox
										options={clientOptions_data}
										value={selectedClientOption}
										onChange={handleClientChange}
										placeholder="Buscar cliente..."
										isLoading={clientsPending}
										className="w-full"
									/>
								)}
							</div>
						) : null}

						{/* Show Worker Selection if client was pre-selected, or if editing existing session */}
						{preSelectedClient ? (
							<div>
								{workerError ? (
									<div className="alert alert-error">
										<span>
											Error: {getErrorResult(workerError).error.message}
										</span>
									</div>
								) : (
									<ComboBox
										options={workerOptions_data}
										value={selectedWorkerOption}
										onChange={handleWorkerChange}
										placeholder="Buscar trabajador..."
										isLoading={workersPending}
										className="w-full"
									/>
								)}
							</div>
						) : null}

						{/* Note field - always show when modal is open */}
						<div>
							<textarea
								className="textarea textarea-bordered w-full resize-none"
								placeholder="Notas (opcional)..."
								rows={3}
								value={note}
								onChange={(e) => {
									setNote(e.target.value);
								}}
								onKeyUp={(e) => {
									if (e.key === "Enter") {
										saveSession();
									}
								}}
							/>
						</div>

						{/* Busy/Free Controls - alongside comboboxes */}
						{!existingSession && preSelectedWorker && (
							<div>
								<button
									type="button"
									className="btn btn-warning w-full"
									onClick={handleMakeBusy}
								>
									Marcar como Ocupado
								</button>
							</div>
						)}

						{/* Delete/Free existing session controls */}
						{existingSession && (
							<div>
								<button
									type="button"
									className={`btn w-full ${existingSession.client.id === "0" ? "btn-success" : "btn-error"}`}
									onClick={handleDelete}
								>
									{existingSession.client.id === "0"
										? "Liberar Horario"
										: "Eliminar Sesión"}
								</button>
							</div>
						)}
					</div>
				</div>
				<div className="modal-action">
					<button type="button" className="btn btn-ghost" onClick={onClose}>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-primary"
						disabled={!selectedClient || !selectedWorker}
						onClick={saveSession}
					>
						Guardar
					</button>
				</div>
			</div>
		</div>
	);
}
