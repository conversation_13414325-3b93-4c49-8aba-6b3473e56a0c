import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { useEffect } from "react";
import { useService } from "~/config/context/serviceProvider";
import ComboBox from "~/core/components/ComboBox";
import { getErrorResult } from "~/core/utils/effectErrors";
import { clientOptions } from "~/modules/client/hooks/client-options";
import { workerOptions } from "~/modules/worker/hooks/worker-options";
import { sessionActions, sessionStore } from "../store/session";

export default function NameDisplaySelect() {
	const {
		selectedClient,
		selectedWorker,
		showNames,
		nameDisplayClient,
		nameDisplayWorker,
	} = useStore(sessionStore);
	const svc = useService();

	const {
		data: clients,
		isError: clientsError,
		error: clientError,
		isPending: clientsPending,
	} = useQuery(clientOptions(svc));

	const {
		data: workers,
		isError: workersError,
		error: workerError,
		isPending: workersPending,
	} = useQuery(workerOptions(svc));

	useEffect(() => {
		if (clientError) {
			console.log(getErrorResult(clientError).error);
		}
		if (workerError) {
			console.log(getErrorResult(workerError).error);
		}
	}, [clientError, workerError]);

	// Don't show this component if showNames is true or if no client/worker is selected
	if (showNames || (!selectedClient && !selectedWorker)) {
		return null;
	}

	const clientOptions_data =
		clients?.map((client) => ({
			value: client.id,
			label: `${client.person.name} ${client.person.fatherLastName} ${client.person.motherLastName}`,
		})) || [];

	const workerOptions_data =
		workers?.map((worker) => ({
			value: worker.id,
			label: `${worker.person.name} ${worker.person.fatherLastName} ${worker.person.motherLastName}`,
		})) || [];

	const handleClientChange = (
		option: { value: string | number; label: string } | null,
	) => {
		if (option) {
			const client = clients?.find((c) => c.id === option.value);
			sessionActions.setNameDisplayClient(client || null);
		} else {
			sessionActions.setNameDisplayClient(null);
		}
	};

	const handleWorkerChange = (
		option: { value: string | number; label: string } | null,
	) => {
		if (option) {
			const worker = workers?.find((w) => w.id === option.value);
			sessionActions.setNameDisplayWorker(worker || null);
		} else {
			sessionActions.setNameDisplayWorker(null);
		}
	};

	const selectedClientOption = nameDisplayClient
		? {
				value: nameDisplayClient.id,
				label: `${nameDisplayClient.person.name} ${nameDisplayClient.person.fatherLastName} ${nameDisplayClient.person.motherLastName}`,
				data: nameDisplayClient,
			}
		: null;

	const selectedWorkerOption = nameDisplayWorker
		? {
				value: nameDisplayWorker.id,
				label: `${nameDisplayWorker.person.name} ${nameDisplayWorker.person.fatherLastName} ${nameDisplayWorker.person.motherLastName}`,
				data: nameDisplayWorker,
			}
		: null;

	return (
		<div className="card bg-base-100 shadow-sm">
			<div className="card-body">
				{/* Show client select if worker is selected */}
				{selectedWorker && (
					<div>
						{clientsError ? (
							<div className="alert alert-error text-xs">
								<span>Error: {getErrorResult(clientError).error.message}</span>
							</div>
						) : (
							<ComboBox
								options={clientOptions_data}
								value={selectedClientOption}
								onChange={handleClientChange}
								placeholder="Seleccionar cliente para mostrar nombres..."
								isLoading={clientsPending}
								label="Mostrar nombres de sesiones con cliente"
								className="w-full"
							/>
						)}
					</div>
				)}

				{/* Show worker select if client is selected */}
				{selectedClient && (
					<div>
						{workersError ? (
							<div className="alert alert-error text-xs">
								<span>Error: {getErrorResult(workerError).error.message}</span>
							</div>
						) : (
							<ComboBox
								options={workerOptions_data}
								value={selectedWorkerOption}
								onChange={handleWorkerChange}
								placeholder="Seleccionar trabajador para mostrar nombres..."
								isLoading={workersPending}
								label="Mostrar nombres de sesiones con trabajador"
								className="w-full"
							/>
						)}
					</div>
				)}
			</div>
		</div>
	);
}
