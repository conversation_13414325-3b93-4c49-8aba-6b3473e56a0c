import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { UpdateSession } from "../service/model/session";
import { sessionStore } from "../store/session";
import {
	sessionOptionsByClientAndTurn,
	sessionOptionsByWorkerAndTurn,
} from "./session-options";

export default function useUpdateSession() {
	const service = useService();
	const { session } = service;
	const queryClient = useQueryClient();
	const [selectedClient, selectedWorker, selectedTurn] = useStore(
		sessionStore,
		(state) => [state.selectedClient, state.selectedWorker, state.selectedTurn],
	);

	return useMutation({
		mutationFn: (updatedSession: UpdateSession) =>
			AppRuntime.runPromise(session.update(updatedSession)),
		onMutate: async (updatedSession) => {
			if (selectedClient && selectedTurn) {
				const sessionsByClientQueryKey = sessionOptionsByClientAndTurn(
					service,
					selectedClient.id,
					selectedTurn.id,
				).queryKey;

				await queryClient.cancelQueries({
					queryKey: sessionsByClientQueryKey,
				});

				const sessionsByClient = queryClient.getQueryData(
					sessionsByClientQueryKey,
				);

				if (sessionsByClient) {
					queryClient.setQueryData(
						sessionsByClientQueryKey,
						create(sessionsByClient, (draft) => {
							const index = draft.findIndex((s) => s.id === updatedSession.id);
							if (index !== -1) {
								draft[index] = { ...draft[index], ...updatedSession };
							}
						}),
					);
				}

				return {
					previousSessions: sessionsByClient,
					queryKey: sessionsByClientQueryKey,
				};
			}

			if (selectedWorker && selectedTurn) {
				const sessionsByWorkerQueryKey = sessionOptionsByWorkerAndTurn(
					service,
					selectedWorker.id,
					selectedTurn.id,
				).queryKey;

				await queryClient.cancelQueries({
					queryKey: sessionsByWorkerQueryKey,
				});

				const sessionsByWorker = queryClient.getQueryData(
					sessionsByWorkerQueryKey,
				);

				if (sessionsByWorker) {
					queryClient.setQueryData(
						sessionsByWorkerQueryKey,
						create(sessionsByWorker, (draft) => {
							const index = draft.findIndex((s) => s.id === updatedSession.id);
							if (index !== -1) {
								draft[index] = { ...draft[index], ...updatedSession };
							}
						}),
					);
				}

				return {
					previousSessions: sessionsByWorker,
					queryKey: sessionsByWorkerQueryKey,
				};
			}

			return { previousSessions: [], queryKey: [] };
		},
		onError: (_, __, context) => {
			if (context) {
				queryClient.setQueryData(context.queryKey, context.previousSessions);
			}
		},
		onSettled: (_, __, ___, context) => {
			if (context) {
				queryClient.invalidateQueries({
					queryKey: context.queryKey,
				});
			}
		},
	});
}
