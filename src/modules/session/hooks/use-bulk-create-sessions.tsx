import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { create } from "mutative";
import { Client } from "~/client/service/model/client";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { type CreateManySession, Session } from "../service/model/session";
import { sessionStore } from "../store/session";
import { newPerson } from "../utils/person";
import { sessionOptionsByWorkerAndTurn } from "./session-options";

export default function useBulkCreateSessions() {
	const service = useService();
	const { session } = service;
	const queryClient = useQueryClient();
	const [selectedWorker, selectedTurn] = useStore(sessionStore, (state) => [
		state.selectedWorker,
		state.selectedTurn,
	]);

	return useMutation({
		mutationFn: (sessions: CreateManySession) =>
			AppRuntime.runPromise(session.createMany(sessions)),
		onMutate: async ({ sessions }) => {
			if (selectedWorker && selectedTurn) {
				const sessionsByWorkerQueryKey = sessionOptionsByWorkerAndTurn(
					service,
					selectedWorker.id,
					selectedTurn.id,
				).queryKey;

				await queryClient.cancelQueries({
					queryKey: sessionsByWorkerQueryKey,
				});

				const sessionsByWorker = queryClient.getQueryData(
					sessionsByWorkerQueryKey,
				);

				const newSessionsEl = sessions.map((session) =>
					Session.make({
						id: "new",
						...session,
						client: Client.make({
							id: "new",
							person: newPerson,
							publicLink: null,
							createdAt: null,
							updatedAt: null,
							deletedAt: null,
						}),
						worker: selectedWorker,
						note: session.note || null,
						createdAt: null,
						updatedAt: null,
						deletedAt: null,
					}),
				);

				if (sessionsByWorker) {
					queryClient.setQueryData(
						sessionsByWorkerQueryKey,
						create(sessionsByWorker, (draft) => {
							draft.push(...newSessionsEl);
						}),
					);
				} else {
					queryClient.setQueryData(sessionsByWorkerQueryKey, [
						...newSessionsEl,
					]);
				}

				return {
					previousSessions: sessionsByWorker,
					queryKey: sessionsByWorkerQueryKey,
				};
			}

			return { previousSessions: [], queryKey: [] };
		},

		onError: (_, __, context) => {
			if (context) {
				queryClient.setQueryData(context.queryKey, context.previousSessions);
			}
		},
		onSettled: (_, __, ___, context) => {
			if (context) {
				queryClient.invalidateQueries({
					queryKey: context.queryKey,
				});
			}
		},
	});
}
