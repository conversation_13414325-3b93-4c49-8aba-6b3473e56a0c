import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { CreatePublicClientLink } from "../service/model/publicClientLink";
import { clientOptions } from "./client-options";

export default function useCreatePublicLink() {
	const service = useService();
	const { client } = service;
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (newPublicLink: CreatePublicClientLink) =>
			AppRuntime.runPromise(client.createPublicLink(newPublicLink)),
		onSuccess: (_, variables) => {
			// Invalidate clients list to refresh public link URLs
			queryClient.invalidateQueries({
				queryKey: clientOptions(service).queryKey,
			});
			// Invalidate specific public link query
			queryClient.invalidateQueries({
				queryKey: ["public-link", "client", variables.clientId],
			});
		},
	});
}
