import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { clientOptions } from "./client-options";

export default function useDeletePublicLink() {
	const service = useService();
	const { client } = service;
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (id: string) =>
			AppRuntime.runPromise(client.deletePublicLink(id)),
		onSuccess: () => {
			// Invalidate clients list to refresh public link URLs
			queryClient.invalidateQueries({
				queryKey: clientOptions(service).queryKey,
			});
			// Invalidate all public link queries
			queryClient.invalidateQueries({
				queryKey: ["public-link"],
			});
		},
	});
}
