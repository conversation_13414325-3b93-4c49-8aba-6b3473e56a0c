import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { UpdatePublicClientLink } from "../service/model/publicClientLink";
import { publicLinkByClientOptions } from "./client-options";

export default function useUpdatePublicLink() {
	const service = useService();
	const { client } = service;
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (updatePublicLink: UpdatePublicClientLink) =>
			AppRuntime.runPromise(client.updatePublicLink(updatePublicLink)),
		onMutate: async (updatedPublicLink) => {
			const queryKey = publicLinkByClientOptions(
				service,
				updatedPublicLink.url,
			).queryKey;

			await queryClient.invalidateQueries({
				queryKey,
			});

			const previousPublicLink = queryClient.getQueryData(queryKey);

			if (previousPublicLink) {
				queryClient.setQueryData(
					queryKey,
					create(previousPublicLink, (draft) => {
						draft.scheduleId = updatedPublicLink.scheduleId;
						draft.workerIds = updatedPublicLink.workerIds;
						draft.clientId = updatedPublicLink.clientId;
						draft.url = updatedPublicLink.url;
					}),
				);
			} else {
				queryClient.setQueryData(queryKey, {
					id: updatedPublicLink.id,
					scheduleId: updatedPublicLink.scheduleId,
					workerIds: updatedPublicLink.workerIds,
					clientId: updatedPublicLink.clientId,
					url: updatedPublicLink.url,
					createdAt: null,
					updatedAt: null,
					deletedAt: null,
				});
			}
			return { previousPublicLink, queryKey };
		},
		onError: (_, __, context) => {
			if (context?.queryKey) {
				queryClient.setQueryData(
					context?.queryKey,
					context?.previousPublicLink,
				);
			}
		},
		onSettled: (_, __, ___, context) => {
			if (context?.queryKey) {
				queryClient.invalidateQueries({
					queryKey: context.queryKey,
				});
			}
		},
	});
}
