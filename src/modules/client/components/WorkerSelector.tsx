import ComboBox from "~/core/components/ComboBox";
import type { WorkerInfo } from "../service/model/publicClientLink";

interface WorkerSelectorProps {
	workers: WorkerInfo[];
	selectedWorker: WorkerInfo | null;
	onWorkerChange: (worker: WorkerInfo | null) => void;
}

export default function WorkerSelector({
	workers,
	selectedWorker,
	onWorkerChange,
}: WorkerSelectorProps) {
	// Transform workers to ComboBox options
	const workerOptions_data = workers.map((worker) => ({
		value: worker.id,
		label: `${worker.name} ${worker.fatherLastName} ${worker.motherLastName}`,
	}));

	// Find selected worker option
	const selectedWorkerOption = selectedWorker
		? {
				value: selectedWorker.id,
				label: `${selectedWorker.name} ${selectedWorker.fatherLastName} ${selectedWorker.motherLastName}`,
			}
		: null;

	const handleWorkerChange = (
		option: { value: string; label: string } | null,
	) => {
		if (!option) {
			onWorkerChange(null);
			return;
		}

		const worker = workers.find((w) => w.id === option.value);
		onWorkerChange(worker || null);
	};

	return (
		<div className="form-control w-full">
			<ComboBox
				options={workerOptions_data}
				value={selectedWorkerOption}
				// @ts-ignore
				onChange={handleWorkerChange}
				placeholder="Seleccionar trabajador..."
				className="w-full"
			/>
		</div>
	);
}
