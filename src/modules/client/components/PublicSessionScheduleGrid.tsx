import React from "react";
import type {
	ClientInfo,
	ScheduleInfo,
	SessionInfo,
	TurnInfo,
	WorkerInfo,
} from "../service/model/publicClientLink";

interface PublicSessionScheduleGridProps {
	sessions: SessionInfo[];
	schedule: ScheduleInfo;
	turn: TurnInfo;
	selectedWorker: WorkerInfo | null;
	client: ClientInfo;
	sessionDuration: number; // in minutes
	breakDuration: number; // in minutes
}
interface TimeSlot {
	start: number;
	end: number;
	label: string;
}
export default function PublicSessionScheduleGrid({
	sessions,
	turn,
	schedule,
	selectedWorker,
	client,
	sessionDuration,
	breakDuration,
}: PublicSessionScheduleGridProps) {
	const minutesToTimeString = (minutes: number) => {
		const hours = Math.floor(minutes / 60);
		const mins = minutes % 60;
		const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours;
		return `${displayHours}:${mins.toString().padStart(2, "0")}`;
	};

	const generateTimeSlots = (): TimeSlot[] => {
		const slots: TimeSlot[] = [];
		const sessionDuration = schedule.sessionDuration;
		const breakDuration = schedule.breakDuration;

		// Convert start and end times to minutes
		const startTimeInMinutes =
			Math.floor(turn.startTime / 100) * 60 + (turn.startTime % 100);
		const endTimeInMinutes =
			Math.floor(turn.endTime / 100) * 60 + (turn.endTime % 100);

		let currentTime = startTimeInMinutes;

		while (currentTime < endTimeInMinutes) {
			const sessionEnd = currentTime + sessionDuration;

			// Check if the complete session fits within the time window
			if (sessionEnd > endTimeInMinutes) {
				break; // Not enough time for a complete session
			}

			slots.push({
				start: currentTime,
				end: sessionEnd,
				label: "Sesión",
			});

			// Move to next session time (including break duration for spacing)
			currentTime = sessionEnd + breakDuration;
		}

		return slots;
	};

	const timeSlots = generateTimeSlots();
	const days = [
		"Domingo",
		"Lunes",
		"Martes",
		"Miércoles",
		"Jueves",
		"Viernes",
		"Sábado",
	];

	// Get mobile abbreviated day names (first 3 letters)
	const getMobileDayName = (day: string) => day.substring(0, 3);

	// Filter sessions based on selected worker and turn
	const filteredSessions = sessions.filter((session) => {
		const matchesTurn = session.turnId === turn.id;
		const matchesWorker = selectedWorker
			? session.workerId === selectedWorker.id
			: true;
		return matchesTurn && matchesWorker;
	});

	const getSessionForSlot = (dayIndex: number, timeIndex: number) => {
		return filteredSessions.find(
			(session) => session.day === dayIndex && session.time === timeIndex,
		);
	};

	const getIsAssigned = (session: SessionInfo) => {
		return session.isAssigned;
	};

	const getClientDisplayName = () => {
		return `${client.name} ${client.fatherLastName} ${client.motherLastName}`;
	};

	return (
		<div className="overflow-x-auto">
			<div
				className={`grid grid-cols-8 gap-0 text-xs sm:gap-1 grid-rows-[auto_repeat(${timeSlots.length},minmax(64px,auto))]`}
			>
				<div className="flex items-center justify-center rounded bg-neutral font-medium text-white sm:p-2">
					Hora
				</div>
				{days.map((day) => (
					<div
						key={day}
						className="flex items-center justify-center rounded bg-base-300 font-medium"
					>
						{/* Show abbreviated day name on mobile, full name on larger screens */}
						<span className="block sm:hidden">{getMobileDayName(day)}</span>
						<span className="hidden sm:block">{day}</span>
					</div>
				))}
				{timeSlots.map((slot, timeIndex) => (
					<React.Fragment key={`${slot.start}-${timeIndex}`}>
						<div className="flex items-center justify-center rounded bg-base-200 font-mono text-xs sm:p-2">
							{minutesToTimeString(slot.start)} -{" "}
							{minutesToTimeString(slot.end)}
						</div>
						{days.map((day, dayIndex) => {
							const existingSession = getSessionForSlot(dayIndex, timeIndex);
							const isOccupied = !!existingSession;
							const isAssigned = isOccupied && getIsAssigned(existingSession);

							return (
								<div key={day} className="flex items-center justify-center">
									<div
										className={`flex h-16 w-full items-center justify-center rounded border-1 border-base-content/25 px-2 py-1 font-medium text-xs transition-colors sm:h-16 ${
											isOccupied ? (isAssigned ? "" : "bg-[#ff5c9b]") : ""
										}`}
									>
										{isOccupied ? (
											isAssigned ? (
												<span className="text-center text-xs leading-tight">
													{getClientDisplayName()}
												</span>
											) : null
										) : (
											<span className="hidden sm:block">Disponible</span>
										)}
									</div>
								</div>
							);
						})}
					</React.Fragment>
				))}
			</div>
		</div>
	);
}
