import { toast } from "react-toastify";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import { getErrorResult } from "~/core/utils/effectErrors";
import useDeletePublicLink from "../../hooks/use-delete-public-link";
import type { PublicClientLink } from "../../service/model/publicClientLink";

export interface DeletePublicClientLinkModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	publicLink: PublicClientLink;
}

export default function DeletePublicClientLinkModal({
	isOpen,
	setIsOpen,
	publicLink,
}: DeletePublicClientLinkModalProps) {
	const { mutate: deletePublicLink, isPending } = useDeletePublicLink();

	const handleDelete = () => {
		deletePublicLink(publicLink.id, {
			onSuccess: () => {
				toast.success("Enlace público eliminado exitosamente");
				setIsOpen(false);
			},
			onError: (_error) => {
				console.log(_error);
				const { error } = getErrorResult(_error);
				toast.error(error.message);
			},
		});
	};

	const handleClose = () => {
		setIsOpen(false);
	};

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Eliminar Enlace Público</h3>
				<div className="py-4">
					<p className="text-base-content/70">
						¿Estás seguro de que deseas eliminar este enlace público? Esta
						acción no se puede deshacer.
					</p>
					<div className="mt-4 rounded-lg bg-base-200 p-4">
						<p className="text-sm">
							<span className="font-medium">Enlace:</span> {publicLink.url}
						</p>
					</div>
				</div>

				<div className="modal-action">
					<button
						type="button"
						className="btn btn-ghost"
						onClick={handleClose}
						disabled={isPending}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={handleDelete}
						disabled={isPending}
					>
						{isPending ? (
							<span className="loading loading-spinner loading-sm" />
						) : (
							"Eliminar"
						)}
					</button>
				</div>
			</div>
		</div>
	);
}
