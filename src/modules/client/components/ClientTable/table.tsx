import {
	getCoreRowModel,
	getFilteredRowModel,
	useReactTable,
} from "@tanstack/react-table";
import { useState } from "react";
import { DebouncedInput } from "~/core/components/DebouncedInput";
import BasicTable from "~/core/components/tables/BasicTable";
import type { Client } from "../../service/model/client";
import { columns } from "./columns";

interface Props {
	clients: Client[];
}

export default function Table({ clients }: Props) {
	const [globalFilter, setGlobalFilter] = useState("");

	const table = useReactTable({
		data: clients,
		columns: columns,
		state: {
			globalFilter,
		},
		getCoreRowModel: getCoreRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
	});
	return (
		<div>
			<DebouncedInput
				value={globalFilter ?? ""}
				onChange={(value) => setGlobalFilter(String(value))}
				className="input my-2"
				placeholder="Buscar..."
			/>
			<BasicTable table={table} />
		</div>
	);
}
