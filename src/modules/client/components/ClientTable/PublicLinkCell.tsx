import { useQuery } from "@tanstack/react-query";
import { Co<PERSON>, Edit, Plus, Trash } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { constants } from "~/config/constants";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { publicLinkByClientOptions } from "../../hooks/client-options";
import type { Client } from "../../service/model/client";
import CreatePublicClientLinkModal from "../CreatePublicClientLinkModal";
import DeletePublicClientLinkModal from "../DeletePublicClientLinkModal";
import UpdatePublicClientLinkModal from "../UpdatePublicClientLinkModal";

interface PublicLinkCellProps {
	client: Client;
	publicLinkUrl: string | null;
}

export default function PublicLinkCell({
	client,
	publicLinkUrl,
}: PublicLinkCellProps) {
	const svc = useService();

	const [isCreateOpen, setIsCreateOpen] = useState(false);
	const [isUpdateOpen, setIsUpdateOpen] = useState(false);
	const [isDeleteOpen, setIsDeleteOpen] = useState(false);

	// Only fetch public link details when we have a URL and need to update/delete
	const {
		data: publicLinkData,
		isLoading,
		error,
	} = useQuery({
		...publicLinkByClientOptions(svc, publicLinkUrl ?? ""),
		enabled: !!publicLinkUrl,
	});

	async function handleCopyLink() {
		if (publicLinkUrl) {
			await navigator.clipboard.writeText(
				`${constants.URL}/public-link/${publicLinkUrl}`,
			);
			toast.success("Enlace copiado al portapapeles");
		}
	}

	const handleUpdateClick = () => {
		setIsUpdateOpen(true);
	};

	const handleDeleteClick = () => {
		setIsDeleteOpen(true);
	};

	useEffect(() => {
		if (error) {
			const {
				error: { details },
			} = getErrorResult(error);
			console.log(details);
		}
	}, [error]);

	return (
		<div className="flex gap-2">
			{publicLinkUrl ? (
				<>
					<button
						type="button"
						className="btn btn-sm btn-primary"
						onClick={handleUpdateClick}
						disabled={isLoading}
					>
						{isLoading ? (
							<span className="loading loading-spinner loading-xs" />
						) : (
							<Edit size={16} />
						)}
					</button>
					<button
						type="button"
						className="btn btn-sm btn-info"
						onClick={handleCopyLink}
					>
						<Copy size={16} />
					</button>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={handleDeleteClick}
						disabled={isLoading}
					>
						{isLoading ? (
							<span className="loading loading-spinner loading-xs" />
						) : (
							<Trash size={16} />
						)}
					</button>
				</>
			) : (
				<button
					type="button"
					className="btn btn-sm btn-primary"
					onClick={() => setIsCreateOpen(true)}
				>
					<Plus size={16} />
				</button>
			)}

			<CreatePublicClientLinkModal
				isOpen={isCreateOpen}
				setIsOpen={setIsCreateOpen}
				client={client}
			/>

			{publicLinkData && (
				<>
					<UpdatePublicClientLinkModal
						isOpen={isUpdateOpen}
						setIsOpen={setIsUpdateOpen}
						publicLink={publicLinkData}
					/>
					<DeletePublicClientLinkModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						publicLink={publicLinkData}
					/>
				</>
			)}
		</div>
	);
}
