import type { TurnInfo } from "../service/model/publicClientLink";

interface TurnSelectorProps {
	turns: TurnInfo[];
	selectedTurn: TurnInfo | null;
	onTurnChange: (turn: TurnInfo | null) => void;
}

export default function TurnSelector({
	turns,
	selectedTurn,
	onTurnChange,
}: TurnSelectorProps) {
	const formatTime = (time: number) => {
		const hours = Math.floor(time / 100);
		const minutes = time % 100;
		const period = hours >= 12 ? "PM" : "AM";
		const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours;
		return `${displayHours}:${minutes.toString().padStart(2, "0")} ${period}`;
	};

	const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
		const turnId = event.target.value;
		if (turnId === "") {
			onTurnChange(null);
		} else {
			const turn = turns.find((t) => t.id === turnId);
			onTurnChange(turn || null);
		}
	};

	return (
		<div className="form-control w-full">
			<select
				id="turn-select"
				className="select select-bordered w-full"
				value={selectedTurn?.id || ""}
				onChange={handleSelectChange}
			>
				<option value="">Seleccionar turno...</option>
				{turns.map((turn) => (
					<option key={turn.id} value={turn.id}>
						{turn.name} ({formatTime(turn.startTime)} -{" "}
						{formatTime(turn.endTime)})
					</option>
				))}
			</select>
		</div>
	);
}
