import { useQuery } from "@tanstack/react-query";
import { <PERSON><PERSON>, Sparkle } from "lucide-react";
import { useEffect } from "react";
import { constants } from "~/config/constants";
import { useService } from "~/config/context/serviceProvider";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import { getErrorResult } from "~/core/utils/effectErrors";
import { scheduleOptions } from "~/modules/schedule/hooks/schedule-options";
import { workerOptions } from "~/modules/worker/hooks/worker-options";
import type { CreatePublicClientLinkModalProps } from "./use-create-public-link-modal";
import useCreatePublicClientLinkModal from "./use-create-public-link-modal";

export default function CreatePublicClientLinkModal({
	isOpen,
	setIsOpen,
	client,
}: CreatePublicClientLinkModalProps) {
	const {
		form,
		handleClose,
		isGenerating,
		handleGenerateLink,
		handleCopyLink,
	} = useCreatePublicClientLinkModal({ isOpen, setIsOpen, client });

	const svc = useService();

	const { data: schedules, error: scheduleError } = useQuery({
		...scheduleOptions(svc),
		enabled: isOpen,
	});

	const {
		data: workers,
		error: workerError,
		isPending: workersPending,
	} = useQuery({
		...workerOptions(svc),
		enabled: isOpen,
	});

	useEffect(() => {
		if (scheduleError) {
			console.log(getErrorResult(scheduleError).error);
		}
		if (workerError) {
			console.log(getErrorResult(workerError).error);
		}
	}, [scheduleError, workerError]);

	const scheduleOptions_data =
		schedules?.map((schedule) => ({
			value: schedule.id,
			label: schedule.name,
		})) || [];

	const workerOptions_data =
		workers?.map((worker) => ({
			value: worker.id,
			label: `${worker.person.name} ${worker.person.fatherLastName} ${worker.person.motherLastName}`,
		})) || [];

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box max-w-2xl">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">
					Crear Enlace Público - {client.person.name}{" "}
					{client.person.fatherLastName}
				</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							{/* Schedule Select */}
							<form.AppField
								name="scheduleId"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Horario"
										placeholder="Seleccionar horario"
										options={scheduleOptions_data}
									/>
								)}
							/>

							{/* Workers Multi Select */}
							<form.AppField
								name="workerIds"
								children={({ FSComboBoxField }) => (
									<FSComboBoxField
										label="Trabajadores"
										placeholder="Seleccionar trabajadores"
										options={workerOptions_data}
										isMultiple={true}
										isLoading={workersPending}
									/>
								)}
							/>

							{/* Generate Link Button */}
							<form.AppField
								name="url"
								children={({ FSTextField, state }) => (
									<FSTextField
										label="Enlace"
										placeholder="Ingresar enlace"
										prefixComponent={
											<span className="text-base-content/70 text-xs">
												{constants.URL}/public-link/
											</span>
										}
										suffixComponent={
											<div>
												<button
													type="button"
													className="btn btn-sm btn-ghost"
													onClick={handleCopyLink}
													disabled={isGenerating || !state.value}
												>
													{isGenerating ? (
														<span className="loading loading-spinner loading-sm" />
													) : (
														<Copy size={16} />
													)}
												</button>
												<button
													type="button"
													className="btn btn-sm btn-ghost"
													onClick={handleGenerateLink}
													disabled={isGenerating}
												>
													{isGenerating ? (
														<span className="loading loading-spinner loading-sm" />
													) : (
														<Sparkle size={16} />
													)}
												</button>
											</div>
										}
									/>
								)}
							/>

							{/* Generated URL Display */}
						</fieldset>

						<div className="modal-action">
							<button type="button" className="btn" onClick={handleClose}>
								Cancelar
							</button>
							<form.SubscribeButton label="Crear" />
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
