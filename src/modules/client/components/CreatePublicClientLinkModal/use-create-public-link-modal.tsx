import { toast } from "react-toastify";
import { constants } from "~/config/constants";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useCreatePublicLink from "../../hooks/use-create-public-link";
import useGeneratePublicLink from "../../hooks/use-generate-public-link";
import type { Client } from "../../service/model/client";
import { CreatePublicClientLinkSchema } from "./schema";

export interface CreatePublicClientLinkModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	client: Client;
}

const defaultValues = {
	scheduleId: "",
	url: "",
	workerIds: [],
} as CreatePublicClientLinkSchema;

export default function useCreatePublicClientLinkModal({
	setIsOpen,
	client,
}: CreatePublicClientLinkModalProps) {
	const { mutate: createPublicLink } = useCreatePublicLink();
	const { mutate: generatePublicLink, isPending: isGenerating } =
		useGeneratePublicLink();

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreatePublicClientLinkSchema,
		},
		onSubmit: ({ value }) => {
			createPublicLink(
				{
					scheduleId: value.scheduleId,
					workerIds: value.workerIds,
					url: value.url,
					clientId: client.id,
				},
				{
					onSuccess: () => {
						toast.success("Enlace público creado exitosamente");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	const handleGenerateLink = () => {
		generatePublicLink(undefined, {
			onSuccess: (url) => {
				form.setFieldValue("url", url);
				form.validateField("url", "change");
				toast.success("Enlace generado exitosamente");
			},
			onError: (_error) => {
				console.log(_error);
				const { error } = getErrorResult(_error);
				toast.error(error.message);
			},
		});
	};

	const handleCopyLink = async () => {
		if (form.getFieldValue("url")) {
			try {
				await navigator.clipboard.writeText(
					`${constants.URL}/public-link/${form.getFieldValue("url")}`,
				);
				toast.success("Enlace copiado al portapapeles");
			} catch (error) {
				toast.error("Error al copiar el enlace");
			}
		}
	};

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		isGenerating,
		handleGenerateLink,
		handleCopyLink,
	};
}
