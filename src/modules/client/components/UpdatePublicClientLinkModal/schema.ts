import * as v from "valibot";

export const UpdatePublicClientLinkSchema = v.object({
	scheduleId: v.pipe(
		v.string("Debe seleccionar un horario"),
		v.minLength(1, "Debe seleccionar un horario"),
	),
	workerIds: v.pipe(
		v.array(v.string()),
		v.minLength(1, "Debe seleccionar al menos un trabajador"),
	),
	url: v.pipe(
		v.string("Debe ingresar un enlace"),
		v.minLength(5, "Debe tener al menos 5 caracteres"),
	),
});

export type UpdatePublicClientLinkSchema = v.InferOutput<
	typeof UpdatePublicClientLinkSchema
>;
