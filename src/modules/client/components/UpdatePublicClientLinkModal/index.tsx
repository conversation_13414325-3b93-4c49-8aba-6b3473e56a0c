import { useQuery } from "@tanstack/react-query";
import { <PERSON><PERSON>, <PERSON>rkle } from "lucide-react";
import { useEffect } from "react";
import { constants } from "~/config/constants";
import { useService } from "~/config/context/serviceProvider";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import { getErrorResult } from "~/core/utils/effectErrors";
import { scheduleOptions } from "~/modules/schedule/hooks/schedule-options";
import { workerOptions } from "~/modules/worker/hooks/worker-options";
import type { UpdatePublicClientLinkModalProps } from "./use-update-public-link-modal";
import useUpdatePublicClientLinkModal from "./use-update-public-link-modal";

export default function UpdatePublicClientLinkModal({
	isOpen,
	setIsOpen,
	publicLink,
}: UpdatePublicClientLinkModalProps) {
	const {
		form,
		handleClose,
		isGenerating,
		handleGenerateLink,
		handleCopyLink,
	} = useUpdatePublicClientLinkModal({ isOpen, setIsOpen, publicLink });

	const svc = useService();

	const { data: schedules, error: scheduleError } = useQuery({
		...scheduleOptions(svc),
		enabled: isOpen,
	});

	const {
		data: workers,
		error: workerError,
		isPending: workersPending,
	} = useQuery({
		...workerOptions(svc),
		enabled: isOpen,
	});

	useEffect(() => {
		if (scheduleError) {
			console.log(getErrorResult(scheduleError).error);
		}
		if (workerError) {
			console.log(getErrorResult(workerError).error);
		}
	}, [scheduleError, workerError]);

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Actualizar Enlace Público</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<div className="space-y-4 py-4">
						{/* Schedule Select */}
						<form.AppField
							name="scheduleId"
							children={({ FSSelectField }) => (
								<FSSelectField
									label="Horario"
									placeholder="Seleccionar horario"
									options={
										schedules?.map((schedule) => ({
											value: schedule.id,
											label: schedule.name,
										})) || []
									}
								/>
							)}
						/>

						{/* Workers Multi-Select */}
						<form.AppField
							name="workerIds"
							children={({ FSComboBoxField }) => (
								<FSComboBoxField
									label="Trabajadores"
									placeholder="Seleccionar trabajadores"
									options={
										workers?.map((worker) => ({
											value: worker.id,
											label: `${worker.person.name} ${worker.person.fatherLastName} ${worker.person.motherLastName}`,
										})) || []
									}
									isMultiple={true}
								/>
							)}
						/>

						{/* Generate Link Button */}
						<form.AppField
							name="url"
							children={({ FSTextField, state }) => (
								<FSTextField
									label="Enlace"
									placeholder="Ingresar enlace"
									prefixComponent={
										<span className="text-base-content/70 text-xs">
											{constants.URL}/public-link/
										</span>
									}
									suffixComponent={
										<div>
											<button
												type="button"
												className="btn btn-sm btn-ghost"
												onClick={handleCopyLink}
												disabled={isGenerating || !state.value}
											>
												{isGenerating ? (
													<span className="loading loading-spinner loading-sm" />
												) : (
													<Copy size={16} />
												)}
											</button>
											<button
												type="button"
												className="btn btn-sm btn-ghost"
												onClick={handleGenerateLink}
												disabled={isGenerating}
											>
												{isGenerating ? (
													<span className="loading loading-spinner loading-sm" />
												) : (
													<Sparkle size={16} />
												)}
											</button>
										</div>
									}
								/>
							)}
						/>
					</div>

					<div className="modal-action">
						<button
							type="button"
							className="btn btn-ghost"
							onClick={handleClose}
						>
							Cancelar
						</button>
						<button
							type="submit"
							className="btn btn-primary"
							disabled={!form.state.canSubmit || form.state.isSubmitting}
						>
							{form.state.isSubmitting ? (
								<span className="loading loading-spinner loading-sm" />
							) : (
								"Actualizar"
							)}
						</button>
					</div>
				</form>
			</div>
		</div>
	);
}
