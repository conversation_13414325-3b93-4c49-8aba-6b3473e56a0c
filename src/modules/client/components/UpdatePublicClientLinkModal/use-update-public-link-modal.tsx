import { toast } from "react-toastify";
import { constants } from "~/config/constants";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import type { Worker } from "~/modules/worker/service/model/worker";
import useGeneratePublicLink from "../../hooks/use-generate-public-link";
import useUpdatePublicLink from "../../hooks/use-update-public-link";
import type { PublicClientLink } from "../../service/model/publicClientLink";
import { UpdatePublicClientLinkSchema } from "./schema";

export interface UpdatePublicClientLinkModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	publicLink: PublicClientLink;
}

export default function useUpdatePublicClientLinkModal({
	setIsOpen,
	publicLink,
}: UpdatePublicClientLinkModalProps) {
	const { mutate: updatePublicLink } = useUpdatePublicLink();
	const { mutate: generatePublicLink, isPending: isGenerating } =
		useGeneratePublicLink();

	const defaultValues = {
		scheduleId: publicLink.scheduleId,
		url: publicLink.url,
		workerIds: publicLink.workerIds,
	} as UpdatePublicClientLinkSchema;

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: UpdatePublicClientLinkSchema,
		},
		onSubmit: ({ value }) => {
			updatePublicLink(
				{
					id: publicLink.id,
					scheduleId: value.scheduleId,
					workerIds: value.workerIds,
					url: value.url,
					clientId: publicLink.clientId,
				},
				{
					onSuccess: () => {
						toast.success("Enlace público actualizado exitosamente");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	const handleGenerateLink = () => {
		generatePublicLink(undefined, {
			onSuccess: (url) => {
				form.setFieldValue("url", url);
				form.validateField("url", "change");
				toast.success("Enlace generado exitosamente");
			},
			onError: (_error) => {
				console.log(_error);
				const { error } = getErrorResult(_error);
				toast.error(error.message);
			},
		});
	};

	const handleCopyLink = async () => {
		if (form.getFieldValue("url")) {
			try {
				await navigator.clipboard.writeText(
					`${constants.URL}/public-link/${form.getFieldValue("url")}`,
				);
				toast.success("Enlace copiado al portapapeles");
			} catch (error) {
				toast.error("Error al copiar el enlace");
			}
		}
	};

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		isGenerating,
		handleGenerateLink,
		handleCopyLink,
	};
}
