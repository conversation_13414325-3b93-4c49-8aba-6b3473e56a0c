import { <PERSON>ttp<PERSON><PERSON> } from "@effect/platform";
import { <PERSON>, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import type { CreateClient, UpdateClient } from "../../model/client";
import type {
	CreatePublicClientLink,
	UpdatePublicClientLink,
} from "../../model/publicClientLink";
import { ClientRepository } from "../../model/repository";
import {
	ClientFromApi,
	ClientLinkResponseFromApi,
	ClientListFromApi,
	CreateClientApiFromCreateClient,
	CreateClientApiResponse,
	CreatePublicClientLinkApiFromCreatePublicClientLink,
	GeneratePublicLinkResponse,
	PublicClientLinkFromApi,
	UpdateClientApiFromUpdateClient,
	UpdatePublicClientLinkApiFromUpdatePublicClientLink,
} from "./dto";

const baseUrl = "/v1/clients";

const makeClientApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(ClientListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(ClientFromApi))),
		create: (client: CreateClient) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateClientApiFromCreateClient)(client),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateClientApiResponse))),
		update: (client: UpdateClient) =>
			httpClient
				.put(`${baseUrl}/${client.id}`, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateClientApiFromUpdateClient)(client),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),

		// Public Client Link
		generatePublicLink: () =>
			httpClient
				.get(`${baseUrl}/public-link/generate`)
				.pipe(Effect.flatMap(handleDResponse(GeneratePublicLinkResponse))),
		createPublicLink: (link: CreatePublicClientLink) =>
			httpClient
				.post(`${baseUrl}/public-link`, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(
							CreatePublicClientLinkApiFromCreatePublicClientLink,
						)(link),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		updatePublicLink: (link: UpdatePublicClientLink) =>
			httpClient
				.put(`${baseUrl}/public-link`, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(
							UpdatePublicClientLinkApiFromUpdatePublicClientLink,
						)(link),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		getClientLink: (url: string) =>
			httpClient
				.get(`${baseUrl}/public-link/${url}`)
				.pipe(Effect.flatMap(handleDResponse(ClientLinkResponseFromApi))),
		getPublicLinkByClientId: (url: string) =>
			httpClient
				.get(`${baseUrl}/public-link/get-by-url/${url}`)
				.pipe(Effect.flatMap(handleDResponse(PublicClientLinkFromApi))),
		deletePublicLink: (id: string) =>
			httpClient
				.del(`${baseUrl}/${id}/public-link`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const clientApiRepoLive = Layer.effect(
	ClientRepository,
	makeClientApiRepo,
);
