import { Link } from "@tanstack/react-router";
import { Calendar, Clock, FileCheck, Home, UserCog, Users } from "lucide-react";
import { useSidebar } from "../context/SidebarContext";

export default function Sidebar() {
	const { isCollapsed, isMobile } = useSidebar();

	return (
		<div
			className={`h-full bg-base-300 shadow-lg transition-all duration-300 ease-in-out ${
				isMobile
					? isCollapsed
						? "-translate-x-full w-64"
						: "w-64 translate-x-0"
					: isCollapsed
						? "w-16"
						: "w-64"
			}`}
		>
			<div className="p-4">
				{/* Logo/Title */}
				<div className="mb-6 flex items-center justify-center">
					{isCollapsed ? (
						<div className="flex h-8 w-8 items-center justify-center rounded bg-[#0641ab] font-bold text-sm text-white">
							S
						</div>
					) : (
						<h2 className="text-center font-bold text-2xl text-[#0641ab]">
							Schedhold
						</h2>
					)}
				</div>

				{/* Navigation Menu */}
				<ul
					className={`menu w-full rounded-box bg-base-200 ${isCollapsed ? "menu-sm" : "menu-lg"}`}
				>
					<li>
						<Link
							to="/admin"
							className={`flex items-center hover:bg-base-300 ${
								isCollapsed ? "justify-center px-2" : "gap-3"
							}`}
							title={isCollapsed ? "Home" : undefined}
						>
							<Home className="h-5 w-5 flex-shrink-0" />
							{!isCollapsed && <span>Home</span>}
						</Link>
					</li>
					<li>
						<Link
							to="/admin/clients"
							className={`flex items-center hover:bg-base-300 ${
								isCollapsed ? "justify-center px-2" : "gap-3"
							}`}
							title={isCollapsed ? "Clientes" : undefined}
						>
							<Users className="h-5 w-5 flex-shrink-0" />
							{!isCollapsed && <span>Clientes</span>}
						</Link>
					</li>
					<li>
						<Link
							to="/admin/workers"
							className={`flex items-center hover:bg-base-300 ${
								isCollapsed ? "justify-center px-2" : "gap-3"
							}`}
							title={isCollapsed ? "Trabajadores" : undefined}
						>
							<UserCog className="h-5 w-5 flex-shrink-0" />
							{!isCollapsed && <span>Trabajadores</span>}
						</Link>
					</li>
					<li>
						<Link
							to="/admin/schedules"
							className={`flex items-center hover:bg-base-300 ${
								isCollapsed ? "justify-center px-2" : "gap-3"
							}`}
							title={isCollapsed ? "Horarios" : undefined}
						>
							<Calendar className="h-5 w-5 flex-shrink-0" />
							{!isCollapsed && <span>Horarios</span>}
						</Link>
					</li>
					<li>
						<Link
							to="/admin/sessions"
							className={`flex items-center hover:bg-base-300 ${
								isCollapsed ? "justify-center px-2" : "gap-3"
							}`}
							title={isCollapsed ? "Sesiones" : undefined}
						>
							<FileCheck className="h-5 w-5 flex-shrink-0" />
							{!isCollapsed && <span>Sesiones</span>}
						</Link>
					</li>
				</ul>
			</div>
		</div>
	);
}
