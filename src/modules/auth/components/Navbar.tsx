import { useNavigate } from "@tanstack/react-router";
import { Menu, User } from "lucide-react";
import ThemeSwitcher from "~/core/components/ThemeSwitcher";
import { useSidebar } from "../context/SidebarContext";
import { useLogout } from "../hooks/use-logout";

export default function Navbar() {
	const { mutate } = useLogout();
	const navigate = useNavigate();
	const { toggleSidebar } = useSidebar();

	const handleLogout = () => {
		mutate(undefined, {
			onSettled: () => {
				navigate({
					to: "/login",
				});
			},
		});
	};

	return (
		<div className="navbar w-full bg-neutral text-neutral-content">
			<div className="flex flex-1 items-center gap-3">
				{/* Sidebar Toggle Button */}
				<button
					type="button"
					className="btn btn-ghost btn-circle"
					onClick={toggleSidebar}
					title="Toggle Sidebar"
				>
					<Menu size={20} />
				</button>
				<button type="button" className="btn btn-ghost text-xl">
					Schedhold
				</button>
			</div>
			<div className="flex gap-3">
				<ThemeSwitcher />
				<details className="dropdown dropdown-end">
					<summary className="btn btn-circle avatar">
						<User size={26} />
					</summary>
					<ul className="menu menu-sm dropdown-content z-[1] mt-3 w-52 rounded-box bg-neutral p-2 shadow">
						<li>
							<span>Profile</span>
						</li>
						<li>
							<span>Settings</span>
						</li>
						<li>
							<button type="button" onClick={handleLogout}>
								Logout
							</button>
						</li>
					</ul>
				</details>
			</div>
		</div>
	);
}
