import {
	type ReactNode,
	createContext,
	useContext,
	useEffect,
	useState,
} from "react";

interface SidebarContextType {
	isCollapsed: boolean;
	isMobile: boolean;
	toggleSidebar: () => void;
	collapseSidebar: () => void;
	expandSidebar: () => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

interface SidebarProviderProps {
	children: ReactNode;
}

export function SidebarProvider({ children }: SidebarProviderProps) {
	const [isCollapsed, setIsCollapsed] = useState(false);
	const [isMobile, setIsMobile] = useState(false);

	// Check if we're on mobile and handle responsive behavior
	useEffect(() => {
		const checkMobile = () => {
			const mobile = window.innerWidth < 768; // md breakpoint
			setIsMobile(mobile);
			// On mobile, start with sidebar collapsed
			if (mobile && !isCollapsed) {
				setIsCollapsed(true);
			}
		};

		checkMobile();
		window.addEventListener("resize", checkMobile);
		return () => window.removeEventListener("resize", checkMobile);
	}, [isCollapsed]);

	const toggleSidebar = () => {
		setIsCollapsed((prev) => !prev);
	};

	const collapseSidebar = () => {
		setIsCollapsed(true);
	};

	const expandSidebar = () => {
		setIsCollapsed(false);
	};

	const value: SidebarContextType = {
		isCollapsed,
		isMobile,
		toggleSidebar,
		collapseSidebar,
		expandSidebar,
	};

	return (
		<SidebarContext.Provider value={value}>{children}</SidebarContext.Provider>
	);
}

export function useSidebar() {
	const context = useContext(SidebarContext);
	if (context === undefined) {
		throw new Error("useSidebar must be used within a SidebarProvider");
	}
	return context;
}
