export const calculateAge = (birthDate: string) => {
	const today = new Date();
	const birth = new Date(birthDate);
	const age = today.getFullYear() - birth.getFullYear();
	return today.setFullYear(today.getFullYear() - age) >= birth.getTime()
		? age
		: age - 1;
};

export const calculateAgeWithMonths = (birthDate: string) => {
	const today = new Date();
	const birth = new Date(birthDate);

	let years = today.getFullYear() - birth.getFullYear();
	let months = today.getMonth() - birth.getMonth();

	// If the current month/day is before the birth month/day, subtract a year and add 12 months
	if (months < 0 || (months === 0 && today.getDate() < birth.getDate())) {
		years--;
		months += 12;
	}

	// If the current day is before the birth day, subtract a month
	if (today.getDate() < birth.getDate()) {
		months--;
		if (months < 0) {
			months += 12;
			years--;
		}
	}

	return { years, months };
};
