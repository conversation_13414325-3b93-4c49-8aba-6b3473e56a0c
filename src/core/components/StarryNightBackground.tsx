import { useEffect, useRef } from "react";

interface Star {
	giant: boolean;
	comet: boolean;
	x: number;
	y: number;
	r: number;
	dx: number;
	dy: number;
	fadingOut: boolean | null;
	fadingIn: boolean;
	opacity: number;
	opacityTresh: number;
	do: number;
	reset: () => void;
	fadeIn: () => void;
	fadeOut: () => void;
	draw: (universe: CanvasRenderingContext2D) => void;
	move: () => void;
}

export default function StarryNightBackground() {
	const canvasRef = useRef<HTMLCanvasElement>(null);
	const animationRef = useRef<number>(null);
	const starsRef = useRef<Star[]>([]);
	const universeRef = useRef<CanvasRenderingContext2D | null>(null);
	const dimensionsRef = useRef({ width: 0, height: 0, starCount: 0 });
	const firstRef = useRef(true);

	// Configuration
	const starDensity = 0.15;
	const speedCoeff = 0.03;
	const giantColor = "180,184,240";
	const starColor = "226,225,142";
	const cometColor = "226,225,224";
	const fps =
		navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 4
			? 30
			: 60;

	const getProbability = (percents: number): boolean => {
		return Math.floor(Math.random() * 1000) + 1 < percents * 10;
	};

	const getRandInterval = (min: number, max: number): number => {
		return Math.random() * (max - min) + min;
	};

	const createStar = (): Star => {
		const star = {} as Star;

		star.reset = function () {
			const { width, height } = dimensionsRef.current;
			this.giant = getProbability(3);
			this.comet = this.giant || firstRef.current ? false : getProbability(10);
			this.x = getRandInterval(0, width - 10);
			this.y = getRandInterval(0, height);
			this.r = getRandInterval(1.1, 2.6);
			this.dx =
				getRandInterval(speedCoeff, 6 * speedCoeff) +
				(this.comet ? speedCoeff * getRandInterval(50, 120) : 0);
			this.dy =
				-getRandInterval(speedCoeff, 6 * speedCoeff) -
				(this.comet ? speedCoeff * getRandInterval(50, 120) : 0);
			this.fadingOut = null;
			this.fadingIn = true;
			this.opacity = 0;
			this.opacityTresh = getRandInterval(0.2, 1 - (this.comet ? 0.4 : 0));
			this.do = getRandInterval(0.0005, 0.002) + (this.comet ? 0.001 : 0);
		};

		star.fadeIn = function () {
			if (this.fadingIn) {
				this.fadingIn = !(this.opacity > this.opacityTresh);
				this.opacity += this.do;
			}
		};

		star.fadeOut = function () {
			const { width } = dimensionsRef.current;
			if (this.fadingOut) {
				this.fadingOut = !(this.opacity < 0);
				this.opacity -= this.do / 2;
				if (this.x > width || this.y < 0) {
					this.fadingOut = false;
					this.reset();
				}
			}
		};

		star.draw = function (universe: CanvasRenderingContext2D) {
			universe.beginPath();

			if (this.giant) {
				universe.fillStyle = `rgba(${giantColor},${this.opacity})`;
				universe.arc(this.x, this.y, 2, 0, 2 * Math.PI, false);
			} else if (this.comet) {
				universe.fillStyle = `rgba(${cometColor},${this.opacity})`;
				universe.arc(this.x, this.y, 1.5, 0, 2 * Math.PI, false);
				for (let i = 0; i < 30; i++) {
					universe.fillStyle = `rgba(${cometColor},${this.opacity - (this.opacity / 20) * i})`;
					universe.fillRect(
						this.x - (this.dx / 4) * i,
						this.y - (this.dy / 4) * i - 2,
						2,
						2,
					);
				}
			} else {
				universe.fillStyle = `rgba(${starColor},${this.opacity})`;
				universe.fillRect(this.x, this.y, this.r, this.r);
			}

			universe.closePath();
			universe.fill();
		};

		star.move = function () {
			const { width } = dimensionsRef.current;
			this.x += this.dx;
			this.y += this.dy;
			if (this.fadingOut === false) {
				this.reset();
			}
			if (this.x > width - width / 4 || this.y < 0) {
				this.fadingOut = true;
			}
		};

		return star;
	};

	const handleResize = () => {
		if (!canvasRef.current) return;

		const width = window.innerWidth;
		const height = window.innerHeight;
		const starCount = width * starDensity;

		dimensionsRef.current = { width, height, starCount };

		canvasRef.current.width = width;
		canvasRef.current.height = height;

		// Recreate stars with new dimensions
		starsRef.current = [];
		for (let i = 0; i < starCount; i++) {
			const star = createStar();
			star.reset();
			starsRef.current.push(star);
		}
	};

	const draw = () => {
		if (!universeRef.current || !canvasRef.current) return;

		const { width, height } = dimensionsRef.current;
		universeRef.current.clearRect(0, 0, width, height);

		for (const star of starsRef.current) {
			star.move();
			star.fadeIn();
			star.fadeOut();
			star.draw(universeRef.current);
		}

		setTimeout(() => {
			animationRef.current = requestAnimationFrame(draw);
		}, 1000 / fps);
	};

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		if (!canvasRef.current) return;

		universeRef.current = canvasRef.current.getContext("2d");
		if (!universeRef.current) return;

		handleResize();
		draw();

		// Set first to false after 50ms
		setTimeout(() => {
			firstRef.current = false;
		}, 50);

		const resizeHandler = () => handleResize();
		window.addEventListener("resize", resizeHandler);

		return () => {
			if (animationRef.current) {
				cancelAnimationFrame(animationRef.current);
			}
			window.removeEventListener("resize", resizeHandler);
		};
	}, []);

	return (
		<div className="fixed inset-0 h-full w-full">
			<div
				className="h-full w-full"
				style={{
					backgroundImage:
						"radial-gradient(1600px at 70% 120%, rgba(33, 39, 80, 1) 10%, #020409 100%)",
					filter: "contrast(120%)",
				}}
			>
				<canvas ref={canvasRef} id="universe" className="h-full w-full" />
			</div>
		</div>
	);
}
