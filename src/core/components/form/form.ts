import { createFormHook, createFormHookContexts } from "@tanstack/react-form";
import FSComboBoxField from "./FSComboBoxField";
import { FSPasswordField } from "./FSPasswordField";
import { FSSelectField } from "./FSSelectField";
import { FSTextAreaField } from "./FSTextAreaField";
import { FSTextField } from "./FSTextField";
import { FSToggleField } from "./FSToggleField";
import { SubscribeButton } from "./SubscribeButton";

export const { fieldContext, useFieldContext, formContext, useFormContext } =
	createFormHookContexts();

export const { useAppForm, withForm } = createFormHook({
	fieldComponents: {
		FSTextField,
		FSPasswordField,
		FSSelectField,
		FSToggleField,
		FSComboBoxField,
		FSTextAreaField,
	},
	formComponents: {
		SubscribeButton,
	},
	fieldContext,
	formContext,
});
