import { useFieldContext } from "./form";

type Option = { value: string | number; label: string };

interface Props {
	label: string;
	placeholder?: string;
	options: Option[];
	isNumber?: boolean;
}

export function FSSelectField({
	label,
	placeholder,
	options,
	isNumber = false,
}: Props) {
	const field = useFieldContext<number | string>();
	return (
		<fieldset className="fieldset">
			<legend className="fieldset-legend">{label}</legend>
			<select
				className="select w-full"
				value={isNumber ? field.state.value : field.state.value?.toString()}
				// @ts-ignore
				onChange={(e) =>
					field.handleChange(
						// @ts-ignore
						isNumber ? Number(e.target.value) : e.target.value,
					)
				}
			>
				<option disabled value="" selected>
					{placeholder || "Seleccione una opción"}
				</option>
				{options.map((option) => (
					<option key={option.value} value={option.value}>
						{option.label}
					</option>
				))}
			</select>
			{field.state.meta.isTouched && field.state.meta.errors.length
				? field.state.meta.errors.flatMap((error) => (
						<p key={error.message} className="fieldset-label text-error">
							{error.message}
						</p>
					))
				: null}
		</fieldset>
	);
}
