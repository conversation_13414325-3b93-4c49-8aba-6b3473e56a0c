import { useFieldContext } from "./form";

export function FSTextAreaField({
	placeholder,
	rows = 3,
}: {
	placeholder: string;
	rows?: number;
}) {
	const field = useFieldContext<string>();
	return (
		<div className="w-full">
			<textarea
				className="textarea textarea-bordered w-full resize-none"
				placeholder={placeholder}
				rows={rows}
				value={field.state.value || ""}
				onChange={(e) => field.handleChange(e.target.value)}
			/>
			{field.state.meta.isTouched && field.state.meta.errors.length
				? field.state.meta.errors.flatMap((error) => (
						<p key={error.message} className="mt-1 text-error text-sm">
							{error.message}
						</p>
					))
				: null}
		</div>
	);
}
