import type { ClassValue } from "clsx";
import { MultipleComboBox } from "./Multiple";
import { SingleComboBox } from "./Single";

export interface Option {
	value: number | string;
	label: string;
}

export type Size = "xs" | "sm" | "md" | "lg" | "xl";

export interface BaseComboBoxProps {
	label?: string;
	isLoading?: boolean;
	options: Option[];
	placeholder?: string;
	className?: ClassValue;
	hideReset?: boolean;
	size?: Size;
}

export interface MultipleComboBoxProps extends BaseComboBoxProps {
	isMultiple: true;
	defaultSelected?: Option[];
	value?: Option[];
	onChange: (selectedItems: Option[]) => void;
}

export interface SingleComboBoxProps extends BaseComboBoxProps {
	isMultiple?: false;
	defaultSelected?: Option | null;
	value?: Option | null;
	onChange: (selectedItem: Option | null) => void;
}

export type ComboBoxProps = MultipleComboBoxProps | SingleComboBoxProps;

// Main ComboBox component
export default function ComboBox(props: ComboBoxProps) {
	if (props.isMultiple) {
		return <MultipleComboBox {...props} />;
	}
	return <SingleComboBox {...props} />;
}
