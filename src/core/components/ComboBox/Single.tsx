import { useCombobox } from "downshift";
import { ArrowDown, <PERSON>Up, X } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useMutative } from "use-mutative";
import { cn } from "~/core/utils/classes";
import type { Option, SingleComboBoxProps } from ".";

// Single selection ComboBox
export function SingleComboBox({
	value = null,
	isLoading = false,
	options,
	defaultSelected = null,
	onChange,
	placeholder = "Seleccionar...",
	className,
	size = "md",
	label,
	hideReset = false,
}: SingleComboBoxProps) {
	const [items, setItems] = useMutative<Option[]>(options);
	const [inputValue, setInputValue] = useState(
		defaultSelected?.label || value?.label || "",
	);

	useEffect(() => {
		setInputValue(defaultSelected?.label || value?.label || "");
	}, [defaultSelected, value]);

	// Filter items based on input
	const filterItems = useCallback(
		(searchValue: string) => {
			setItems((draft) => {
				// Reset to original options
				for (let i = 0; i < options.length; i++) {
					// @ts-ignore
					draft[i] = options[i];
				}
				draft.length = options.length;

				// Filter based on search
				if (searchValue) {
					let keepIndex = 0;
					const lowerSearchValue = searchValue.toLowerCase();

					for (let i = 0; i < draft.length; i++) {
						const item = draft[i];
						if (item?.label.toLowerCase().includes(lowerSearchValue)) {
							draft[keepIndex] = item;
							keepIndex++;
						}
					}
					draft.length = keepIndex;
				}
			});
		},
		[options, setItems],
	);

	const {
		isOpen,
		getToggleButtonProps,
		getMenuProps,
		getInputProps,
		highlightedIndex,
		getItemProps,
		reset,
		getLabelProps,
	} = useCombobox({
		items,
		defaultSelectedItem: defaultSelected || value,
		inputValue,
		itemToString: (item) => item?.label ?? "",
		onInputValueChange({ inputValue: newInputValue }) {
			setInputValue(newInputValue || "");
			filterItems(newInputValue || "");
		},
		onSelectedItemChange: ({ selectedItem }) => {
			onChange(selectedItem);
		},
		onIsOpenChange: ({ isOpen: newIsOpen, selectedItem }) => {
			if (!newIsOpen) {
				setInputValue(selectedItem?.label || "");
			}
		},
	});

	useEffect(() => {
		setItems(options);
	}, [options, setItems]);

	useEffect(() => {
		if (value === null) {
			reset();
		}
	}, [value, reset]);

	const renderMenuItem = useCallback(
		(item: Option, index: number) => (
			<li
				key={`${item.value}-${index}`}
				className={cn(
					"flex cursor-pointer flex-col px-3 py-2 shadow-sm",
					highlightedIndex === index && "bg-base-content text-base-200",
					value?.value === item.value && "font-bold",
				)}
				{...getItemProps({ item, index })}
			>
				<span>{item.label}</span>
			</li>
		),
		[highlightedIndex, value, getItemProps],
	);

	return (
		<div className={cn("dropdown", className)}>
			<label htmlFor="combo" className="label" {...getLabelProps()}>
				{label}
			</label>
			<div className="join h-fit w-full items-center bg-base-100">
				{isLoading ? (
					<span className="loading loading-dots join-item loading-sm" />
				) : (
					<input
						placeholder={placeholder}
						className="input input-sm input-bordered join-item w-full"
						{...getInputProps()}
					/>
				)}
				{!hideReset && (
					<button
						className="btn btn-sm btn-soft join-item"
						type="button"
						onClick={() => {
							reset();
							onChange(null);
						}}
						disabled={!value}
					>
						<X className="h-5 w-5" />
					</button>
				)}
				<button
					aria-label="toggle menu"
					className="btn btn-sm btn-soft join-item"
					type="button"
					{...getToggleButtonProps()}
				>
					{isOpen ? (
						<ArrowUp className="h-5 w-5" />
					) : (
						<ArrowDown className="h-5 w-5" />
					)}
				</button>
			</div>
			<ul
				className={cn(
					"dropdown-content z-10 w-full rounded-box bg-base-200 shadow-sm",
					(!isOpen || !items.length) && "hidden",
				)}
				{...getMenuProps()}
			>
				{isOpen && items.map(renderMenuItem)}
			</ul>
		</div>
	);
}
