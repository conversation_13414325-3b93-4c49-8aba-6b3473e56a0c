import type { QueryClient } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import {
	HeadContent,
	Outlet,
	Scripts,
	createRootRouteWithContext,
} from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/react-router-devtools";
import type { ReactNode } from "react";
import { ToastContainer } from "react-toastify";
import { ServiceProvider } from "~/config/context/serviceProvider";
import { ThemeProvider, useTheme } from "~/config/context/themeProvider";
import { serviceRegistry } from "~/core/service";
import { getThemeServerFn } from "~/modules/auth/server/theme";
// @ts-ignore
import appCss from "../config/css/app.css?url";

export const Route = createRootRouteWithContext<{
	queryClient: QueryClient;
}>()({
	head: () => ({
		meta: [
			{
				charSet: "utf-8",
			},
			{
				name: "viewport",
				content: "width=device-width, initial-scale=1",
			},
			{
				title: "Schedhold",
			},
		],
		links: [
			{
				rel: "stylesheet",
				href: appCss,
			},
			{
				rel: "preconnect",
				href: "https://fonts.googleapis.com",
			},
			{
				rel: "preconnect",
				href: "https://fonts.gstatic.com",
				crossOrigin: "use-credentials",
			},
			{
				rel: "stylesheet",
				href: "https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap",
			},
		],
	}),
	loader: () => getThemeServerFn(),
	component: RootComponent,
});

function RootComponent() {
	const data = Route.useLoaderData();

	return (
		<ThemeProvider theme={data}>
			<RootDocument>
				<ServiceProvider service={serviceRegistry}>
					<Outlet />
				</ServiceProvider>
			</RootDocument>
		</ThemeProvider>
	);
}

function RootDocument({ children }: Readonly<{ children: ReactNode }>) {
	const { theme } = useTheme();

	return (
		<html lang="es" data-theme={theme} suppressHydrationWarning>
			<head>
				<HeadContent />
			</head>
			<body>
				{children}
				<ToastContainer theme="dark" position="bottom-right" />
				<ReactQueryDevtools />
				<TanStackRouterDevtools />
				<Scripts />
			</body>
		</html>
	);
}
