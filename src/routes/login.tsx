import { createFileRoute } from "@tanstack/react-router";
import LoginForm from "~/auth/components/LoginForm";
import Starry<PERSON>ightBackground from "~/core/components/StarryNightBackground";

export const Route = createFileRoute("/login")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Iniciar Sesión",
			},
		],
	}),
});

function RouteComponent() {
	return (
		<div className="relative min-h-screen overflow-hidden">
			<StarryNightBackground />
			<div className="hero relative z-10 min-h-screen">
				<div className="hero-content flex-col lg:flex-row-reverse">
					<div className="text-center lg:text-left">
						<h1 className="font-bold text-5xl text-white drop-shadow-lg">
							Ingresa ahora!
						</h1>
						<p className="py-6 text-gray-200 drop-shadow-md">
							Sistema de administración de horarios y sesiones psicológicas
						</p>
					</div>
					<div className="card w-full max-w-sm shrink-0 bg-base-100/90 shadow-2xl backdrop-blur-sm">
						<div className="card-body">
							<LoginForm />
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
