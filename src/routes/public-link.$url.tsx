import { createFileRoute } from "@tanstack/react-router";
import PublicLinkView from "~/client/components/PublicLinkView";

export const Route = createFileRoute("/public-link/$url")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Schedhold CC",
			},
		],
	}),
});

function RouteComponent() {
	const { url } = Route.useParams();

	return (
		<div className="container mx-auto">
			<PublicLinkView url={url} />
		</div>
	);
}
