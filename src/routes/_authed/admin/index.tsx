import { Link, createFileRoute } from "@tanstack/react-router";
import { Briefcase, Calendar, FileCheck, Users } from "lucide-react";

export const Route = createFileRoute("/_authed/admin/")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Inicio",
			},
		],
	}),
});

function RouteComponent() {
	return (
		<div className="container mx-auto p-8">
			<div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
				{/* Workers Card */}
				<Link
					to="/admin/workers"
					className="group hover:-translate-y-1 relative overflow-hidden rounded-lg bg-base-100 p-6 shadow-lg transition-all hover:shadow-xl"
				>
					<div className="flex items-center gap-4">
						<div className="rounded-full bg-primary/10 p-3">
							<Users className="h-6 w-6 text-primary" />
						</div>
						<div>
							<h2 className="font-semibold text-base-content text-xl">
								Workers
							</h2>
							<p className="text-base-content/70">Manage your workforce</p>
						</div>
					</div>
					<div className="absolute bottom-0 left-0 h-1 w-full scale-x-0 transform bg-primary transition-transform duration-300 group-hover:scale-x-100" />
				</Link>

				{/* Clients Card */}
				<Link
					to="/admin/clients"
					className="group hover:-translate-y-1 relative overflow-hidden rounded-lg bg-base-100 p-6 shadow-lg transition-all hover:shadow-xl"
				>
					<div className="flex items-center gap-4">
						<div className="rounded-full bg-secondary/10 p-3">
							<Briefcase className="h-6 w-6 text-secondary" />
						</div>
						<div>
							<h2 className="font-semibold text-base-content text-xl">
								Clients
							</h2>
							<p className="text-base-content/70">Manage your clients</p>
						</div>
					</div>
					<div className="absolute bottom-0 left-0 h-1 w-full scale-x-0 transform bg-secondary transition-transform duration-300 group-hover:scale-x-100" />
				</Link>

				{/* Schedules Card */}
				<Link
					to="/admin/schedules"
					className="group hover:-translate-y-1 relative overflow-hidden rounded-lg bg-base-100 p-6 shadow-lg transition-all hover:shadow-xl"
				>
					<div className="flex items-center gap-4">
						<div className="rounded-full bg-accent/10 p-3">
							<Calendar className="h-6 w-6 text-accent" />
						</div>
						<div>
							<h2 className="font-semibold text-base-content text-xl">
								Schedules
							</h2>
							<p className="text-base-content/70">Manage work schedules</p>
						</div>
					</div>
					<div className="absolute bottom-0 left-0 h-1 w-full scale-x-0 transform bg-accent transition-transform duration-300 group-hover:scale-x-100" />
				</Link>

				{/* Sessions Card */}
				<Link
					to="/admin/sessions"
					className="group hover:-translate-y-1 relative overflow-hidden rounded-lg bg-base-100 p-6 shadow-lg transition-all hover:shadow-xl"
				>
					<div className="flex items-center gap-4">
						<div className="rounded-full bg-warning/10 p-3">
							<FileCheck className="h-6 w-6 text-warning" />
						</div>
						<div>
							<h2 className="font-semibold text-base-content text-xl">
								Sessions
							</h2>
							<p className="text-base-content/70">Manage client sessions</p>
						</div>
					</div>
					<div className="absolute bottom-0 left-0 h-1 w-full scale-x-0 transform bg-warning transition-transform duration-300 group-hover:scale-x-100" />
				</Link>
			</div>
		</div>
	);
}
