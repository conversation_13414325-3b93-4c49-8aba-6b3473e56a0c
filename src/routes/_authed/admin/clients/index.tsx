import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import ClientTable from "~/modules/client/components/ClientTable";
import CreateClientModal from "~/modules/client/components/CreateClientModal";

export const Route = createFileRoute("/_authed/admin/clients/")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Clientes",
			},
		],
	}),
});

function RouteComponent() {
	const [isOpen, setIsOpen] = useState(false);

	return (
		<>
			<div className="container mx-auto">
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<button
								type="button"
								className="btn btn-primary"
								onClick={() => setIsOpen(true)}
							>
								Nuevo cliente
							</button>
						</div>
						<ClientTable />
					</div>
				</div>
			</div>
			<CreateClientModal isOpen={isOpen} setIsOpen={setIsOpen} />
		</>
	);
}
