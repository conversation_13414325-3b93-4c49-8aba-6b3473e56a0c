import { Outlet, createFileRoute } from "@tanstack/react-router";
import Sidebar from "~/auth/components/Sidebar";
import Navbar from "~/modules/auth/components/Navbar";
import {
	SidebarProvider,
	useSidebar,
} from "~/modules/auth/context/SidebarContext";

export const Route = createFileRoute("/_authed/admin")({
	component: RouteComponent,
});

function RouteComponent() {
	return (
		<SidebarProvider>
			<AdminLayout />
		</SidebarProvider>
	);
}

function AdminLayout() {
	const { isCollapsed, isMobile, collapseSidebar } = useSidebar();

	return (
		<div className="relative flex h-screen w-full">
			{/* Mobile Overlay */}
			{isMobile && !isCollapsed && (
				<div
					className="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden"
					onClick={collapseSidebar}
					onKeyDown={(e) => {
						if (e.key === "Escape") {
							collapseSidebar();
						}
					}}
					role="button"
					tabIndex={0}
					aria-label="Close sidebar"
				/>
			)}

			{/* Sidebar */}
			<div className={`${isMobile ? "fixed z-50" : "relative"} h-full`}>
				<Sidebar />
			</div>

			{/* Main Content */}
			<div
				className={`flex min-w-0 flex-col transition-all duration-300 ease-in-out ${
					isMobile
						? "w-full"
						: isCollapsed
							? "w-[calc(100%-4rem)]"
							: "w-[calc(100%-16rem)]"
				}`}
			>
				<Navbar />
				<div className="flex-1 overflow-y-auto bg-base-200">
					<div className="container mx-auto px-4 py-8">
						<Outlet />
					</div>
				</div>
			</div>
		</div>
	);
}
