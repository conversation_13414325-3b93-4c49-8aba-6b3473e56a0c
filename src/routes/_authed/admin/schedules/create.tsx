import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import CreateScheduleForm from "~/modules/schedule/components/CreateScheduleForm";

export const Route = createFileRoute("/_authed/admin/schedules/create")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Crear Horario",
			},
		],
	}),
});

function RouteComponent() {
	return (
		<div className="container mx-auto max-w-6xl">
			<div className="mb-6">
				<Link to="/admin/schedules" className="btn btn-ghost">
					<ArrowLeft size={16} />
					Volver a Horarios
				</Link>
			</div>

			<div className="card bg-base-300">
				<div className="card-body">
					<h2 className="card-title mb-6 text-2xl">Crear Nuevo Horario</h2>
					<CreateScheduleForm />
				</div>
			</div>
		</div>
	);
}
